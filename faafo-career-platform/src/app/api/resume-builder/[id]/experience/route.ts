import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { z } from 'zod';

interface RouteParams {
  params: {
    id: string;
  };
}

// Validation schemas
const experienceSchema = z.object({
  company: z.string()
    .min(1, 'Company name is required')
    .max(100, 'Company name too long')
    .trim(),
  position: z.string()
    .min(1, 'Position is required')
    .max(100, 'Position too long')
    .trim(),
  startDate: z.string()
    .refine(date => !isNaN(Date.parse(date)), 'Invalid start date'),
  endDate: z.string()
    .refine(date => !isNaN(Date.parse(date)), 'Invalid end date')
    .optional(),
  isCurrent: z.boolean().default(false),
  description: z.string()
    .max(2000, 'Description too long')
    .optional()
    .transform(val => val?.trim() || null),
  achievements: z.array(z.string().trim())
    .max(10, 'Too many achievements')
    .default([]),
  sortOrder: z.number().int().min(0).default(0),
});

const updateExperienceSchema = experienceSchema.partial().extend({
  experienceId: z.string().uuid('Invalid experience ID'),
});

// Helper functions
function errorResponse(error: string, status: number = 500, code?: string) {
  return NextResponse.json({ success: false, error, code }, { status });
}

function successResponse<T>(data: T, message?: string, status: number = 200) {
  return NextResponse.json({ success: true, data, message }, { status });
}

// Verify resume ownership
async function verifyResumeOwnership(resumeId: string, userId: string) {
  const resume = await prisma.resume.findFirst({
    where: {
      id: resumeId,
      userId,
      isActive: true,
    },
  });
  return !!resume;
}

// GET - Fetch all experiences for a resume
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resumeId = params.id;

    // Verify ownership
    const hasAccess = await verifyResumeOwnership(resumeId, session.user.id);
    if (!hasAccess) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    const experiences = await prisma.resumeExperience.findMany({
      where: {
        resumeId,
      },
      orderBy: {
        sortOrder: 'asc',
      },
    });

    return successResponse(experiences);

  } catch (error) {
    console.error('Error fetching experiences:', error);
    return errorResponse('Failed to fetch experiences', 500, 'FETCH_ERROR');
  }
}

// POST - Add new experience
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resumeId = params.id;

    // Verify ownership
    const hasAccess = await verifyResumeOwnership(resumeId, session.user.id);
    if (!hasAccess) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    const body = await request.json();
    const validation = experienceSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR'
      );
    }

    const data = validation.data;

    // Validate date logic
    if (data.endDate && !data.isCurrent) {
      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);
      
      if (endDate <= startDate) {
        return errorResponse('End date must be after start date', 400, 'INVALID_DATES');
      }
    }

    // If current position, clear end date
    if (data.isCurrent) {
      data.endDate = undefined;
    }

    const experience = await prisma.resumeExperience.create({
      data: {
        resumeId,
        company: data.company,
        position: data.position,
        startDate: new Date(data.startDate),
        endDate: data.endDate ? new Date(data.endDate) : null,
        isCurrent: data.isCurrent,
        description: data.description,
        achievements: data.achievements,
        sortOrder: data.sortOrder,
      },
    });

    // Update resume's updatedAt timestamp
    await prisma.resume.update({
      where: { id: resumeId },
      data: { updatedAt: new Date() },
    });

    return successResponse(experience, 'Experience added successfully', 201);

  } catch (error) {
    console.error('Error creating experience:', error);
    return errorResponse('Failed to create experience', 500, 'CREATE_ERROR');
  }
}

// PUT - Update experience
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resumeId = params.id;

    // Verify ownership
    const hasAccess = await verifyResumeOwnership(resumeId, session.user.id);
    if (!hasAccess) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    const body = await request.json();
    const validation = updateExperienceSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR'
      );
    }

    const { experienceId, ...updateData } = validation.data;

    // Verify experience exists and belongs to this resume
    const existingExperience = await prisma.resumeExperience.findFirst({
      where: {
        id: experienceId,
        resumeId,
      },
    });

    if (!existingExperience) {
      return errorResponse('Experience not found', 404, 'NOT_FOUND');
    }

    // Validate date logic if dates are being updated
    if (updateData.startDate || updateData.endDate) {
      const startDate = updateData.startDate ? 
        new Date(updateData.startDate) : 
        existingExperience.startDate;
      
      const endDate = updateData.endDate ? 
        new Date(updateData.endDate) : 
        existingExperience.endDate;
      
      if (endDate && endDate <= startDate && !updateData.isCurrent) {
        return errorResponse('End date must be after start date', 400, 'INVALID_DATES');
      }
    }

    // If setting as current position, clear end date
    const finalUpdateData = { ...updateData };
    if (finalUpdateData.isCurrent) {
      finalUpdateData.endDate = undefined;
    }

    const updatedExperience = await prisma.resumeExperience.update({
      where: { id: experienceId },
      data: {
        ...finalUpdateData,
        startDate: finalUpdateData.startDate ? new Date(finalUpdateData.startDate) : undefined,
        endDate: finalUpdateData.endDate ? new Date(finalUpdateData.endDate) : null,
        updatedAt: new Date(),
      },
    });

    // Update resume's updatedAt timestamp
    await prisma.resume.update({
      where: { id: resumeId },
      data: { updatedAt: new Date() },
    });

    return successResponse(updatedExperience, 'Experience updated successfully');

  } catch (error) {
    console.error('Error updating experience:', error);
    return errorResponse('Failed to update experience', 500, 'UPDATE_ERROR');
  }
}
