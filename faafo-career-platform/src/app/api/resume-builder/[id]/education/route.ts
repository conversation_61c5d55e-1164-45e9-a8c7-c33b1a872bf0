import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schemas
const idSchema = z.string().uuid('Invalid ID format');

const createEducationSchema = z.object({
  institution: z.string()
    .min(1, 'Institution is required')
    .max(100, 'Institution name too long')
    .trim(),
  degree: z.string()
    .min(1, 'Degree is required')
    .max(100, 'Degree name too long')
    .trim(),
  fieldOfStudy: z.string()
    .max(100, 'Field of study too long')
    .optional()
    .transform(val => val?.trim() || null),
  startDate: z.string()
    .transform(val => new Date(val)),
  endDate: z.string()
    .optional()
    .transform(val => val ? new Date(val) : null),
  isCurrent: z.boolean().default(false),
  gpa: z.string()
    .max(10, 'GPA too long')
    .optional()
    .transform(val => val?.trim() || null),
  achievements: z.array(z.string()).default([]),
  sortOrder: z.number().int().min(0).default(0),
});

const updateEducationSchema = createEducationSchema.partial().extend({
  id: z.string().uuid('Invalid education ID'),
});

// Route params type
interface RouteParams {
  params: Promise<{ id: string }>;
}

// Standardized response types
interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

function errorResponse(
  error: string, 
  status: number = 400, 
  code?: string, 
  details?: any
): NextResponse<ErrorResponse> {
  return NextResponse.json(
    { success: false, error, code, details },
    { status }
  );
}

function successResponse<T>(
  data: T, 
  message?: string, 
  status: number = 200
): NextResponse<SuccessResponse<T>> {
  return NextResponse.json(
    { success: true, data, message },
    { status }
  );
}

// GET - Fetch all education entries for a resume
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resolvedParams = await params;
    const idValidation = idSchema.safeParse(resolvedParams.id);
    
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    // Verify resume ownership
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!resume) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    // Fetch education entries
    const educations = await prisma.resumeEducation.findMany({
      where: {
        resumeId,
      },
      orderBy: {
        sortOrder: 'asc',
      },
    });

    return successResponse(educations);

  } catch (error) {
    console.error('Error fetching education entries:', error);
    return errorResponse('Failed to fetch education entries', 500, 'FETCH_ERROR');
  }
}

// POST - Create new education entry
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resolvedParams = await params;
    const idValidation = idSchema.safeParse(resolvedParams.id);
    
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    // Verify resume ownership
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!resume) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    const body = await request.json();
    const validation = createEducationSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR',
        validation.error.errors
      );
    }

    const data = validation.data;

    // Create education entry
    const education = await prisma.resumeEducation.create({
      data: {
        resumeId,
        institution: data.institution,
        degree: data.degree,
        fieldOfStudy: data.fieldOfStudy,
        startDate: data.startDate,
        endDate: data.endDate,
        isCurrent: data.isCurrent,
        gpa: data.gpa,
        achievements: data.achievements,
        sortOrder: data.sortOrder,
      },
    });

    return successResponse(education, 'Education entry created successfully', 201);

  } catch (error) {
    console.error('Error creating education entry:', error);
    return errorResponse('Failed to create education entry', 500, 'CREATE_ERROR');
  }
}

// PUT - Update education entry
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resolvedParams = await params;
    const idValidation = idSchema.safeParse(resolvedParams.id);
    
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    const body = await request.json();
    const validation = updateEducationSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR',
        validation.error.errors
      );
    }

    const { id: educationId, ...updateData } = validation.data;

    // Verify ownership
    const education = await prisma.resumeEducation.findFirst({
      where: {
        id: educationId,
        resumeId,
        resume: {
          userId: session.user.id,
          isActive: true,
        },
      },
    });

    if (!education) {
      return errorResponse('Education entry not found', 404, 'NOT_FOUND');
    }

    // Update education entry
    const updatedEducation = await prisma.resumeEducation.update({
      where: { id: educationId },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
    });

    return successResponse(updatedEducation, 'Education entry updated successfully');

  } catch (error) {
    console.error('Error updating education entry:', error);
    return errorResponse('Failed to update education entry', 500, 'UPDATE_ERROR');
  }
}

// DELETE - Delete education entry
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resolvedParams = await params;
    const idValidation = idSchema.safeParse(resolvedParams.id);
    
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    const { searchParams } = new URL(request.url);
    const educationId = searchParams.get('educationId');

    if (!educationId) {
      return errorResponse('Education ID is required', 400, 'MISSING_ID');
    }

    const educationIdValidation = idSchema.safeParse(educationId);
    if (!educationIdValidation.success) {
      return errorResponse('Invalid education ID format', 400, 'INVALID_EDUCATION_ID');
    }

    // Verify ownership
    const education = await prisma.resumeEducation.findFirst({
      where: {
        id: educationId,
        resumeId,
        resume: {
          userId: session.user.id,
          isActive: true,
        },
      },
    });

    if (!education) {
      return errorResponse('Education entry not found', 404, 'NOT_FOUND');
    }

    // Delete education entry
    await prisma.resumeEducation.delete({
      where: { id: educationId },
    });

    return successResponse(
      { id: educationId },
      'Education entry deleted successfully'
    );

  } catch (error) {
    console.error('Error deleting education entry:', error);
    return errorResponse('Failed to delete education entry', 500, 'DELETE_ERROR');
  }
}
