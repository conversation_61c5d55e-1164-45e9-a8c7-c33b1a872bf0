'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  Plus,
  FileText,
  Download,
  Edit3,
  Trash2,
  Eye,
  Star,
  Clock,
  Sparkles,
  Loader2,
  AlertCircle
} from 'lucide-react';

interface Resume {
  id: string;
  title: string;
  firstName: string;
  lastName: string;
  templateId: string | null;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  template?: {
    name: string;
    category: string;
  } | null;
}

interface ApiResponse {
  success: boolean;
  data: {
    resumes: Resume[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
  error?: string;
}

export default function ResumeBuilderPage() {
  const { data: session, status } = useSession();
  const [resumes, setResumes] = useState<Resume[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleting, setDeleting] = useState<string | null>(null);
  const [downloading, setDownloading] = useState<string | null>(null);

  // Fetch resumes from API
  useEffect(() => {
    const fetchResumes = async () => {
      if (status === 'loading') return;
      if (!session) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/resume-builder', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data: ApiResponse = await response.json();

        if (response.ok && data.success) {
          setResumes(data.data.resumes);
        } else {
          setError(data.error || 'Failed to fetch resumes');
        }
      } catch (err) {
        console.error('Error fetching resumes:', err);
        setError('Network error. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchResumes();
  }, [session, status]);

  const handleDeleteResume = async (id: string) => {
    if (!confirm('Are you sure you want to delete this resume?')) {
      return;
    }

    try {
      setDeleting(id);

      const response = await fetch(`/api/resume-builder/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setResumes(resumes.filter(resume => resume.id !== id));
      } else {
        alert(data.error || 'Failed to delete resume');
      }
    } catch (err) {
      console.error('Error deleting resume:', err);
      alert('Network error. Please try again.');
    } finally {
      setDeleting(null);
    }
  };

  const handleDownloadResume = async (resume: Resume) => {
    try {
      setDownloading(resume.id);

      // Create a simple text-based resume for download
      const resumeContent = `
RESUME - ${resume.firstName} ${resume.lastName}

Contact Information:
Email: ${resume.email}
Phone: ${resume.phone || 'Not provided'}
Location: ${resume.location || 'Not provided'}
Website: ${resume.website || 'Not provided'}
LinkedIn: ${resume.linkedin || 'Not provided'}

Professional Summary:
${resume.summary || 'No summary provided'}

Template: ${resume.template?.name || 'Default'}
Last Updated: ${new Date(resume.updatedAt).toLocaleDateString()}
      `;

      const blob = new Blob([resumeContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${resume.firstName}_${resume.lastName}_Resume.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

    } catch (error) {
      console.error('Error downloading resume:', error);
      alert('Failed to download resume');
    } finally {
      setDownloading(null);
    }
  };

  // Show loading state
  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading your resumes...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 mb-2">Error Loading Resumes</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-6 py-2 rounded-xl hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Show login required state
  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <FileText className="h-12 w-12 text-blue-600 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 mb-2">Login Required</h2>
          <p className="text-gray-600 mb-4">Please log in to access your resumes.</p>
          <Link
            href="/auth/signin"
            className="bg-blue-600 text-white px-6 py-2 rounded-xl hover:bg-blue-700 transition-colors inline-block"
          >
            Sign In
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Enhanced Header */}
          <motion.div 
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-12 text-center"
          >
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl">
                <FileText className="h-8 w-8 text-white" />
              </div>
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
              Resume Builder
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Create stunning, ATS-optimized resumes that get you hired. 
              <span className="text-blue-600 font-semibold"> AI-powered</span> content suggestions included.
            </p>
          </motion.div>

          {/* Interactive Action Cards */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-12 grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            <motion.div
              whileHover={{ scale: 1.02, y: -5 }}
              whileTap={{ scale: 0.98 }}
              className="group"
            >
              <Link href="/resume-builder/create">
                <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-white/20 rounded-xl">
                      <Plus className="h-6 w-6" />
                    </div>
                    <Sparkles className="h-5 w-5 opacity-70 group-hover:opacity-100 transition-opacity" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">Create New Resume</h3>
                  <p className="text-blue-100">Start from scratch with AI assistance</p>
                </div>
              </Link>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02, y: -5 }}
              whileTap={{ scale: 0.98 }}
              className="group cursor-pointer"
            >
              <div className="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-white/20 rounded-xl">
                    <FileText className="h-6 w-6" />
                  </div>
                  <Eye className="h-5 w-5 opacity-70 group-hover:opacity-100 transition-opacity" />
                </div>
                <h3 className="text-xl font-bold mb-2">Import from LinkedIn</h3>
                <p className="text-purple-100">Auto-fill with your profile data</p>
              </div>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02, y: -5 }}
              whileTap={{ scale: 0.98 }}
              className="group cursor-pointer"
            >
              <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-white/20 rounded-xl">
                    <Download className="h-6 w-6" />
                  </div>
                  <Plus className="h-5 w-5 opacity-70 group-hover:opacity-100 transition-opacity" />
                </div>
                <h3 className="text-xl font-bold mb-2">Upload Existing</h3>
                <p className="text-green-100">Enhance your current resume</p>
              </div>
            </motion.div>
          </motion.div>

          {/* Enhanced Resume List */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <div className="mb-6 flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">
                Your Resumes ({resumes.length})
              </h2>
            </div>
            
            {resumes.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No resumes yet</h3>
                <p className="text-gray-600 mb-6">Create your first resume to get started</p>
                <Link
                  href="/resume-builder/create"
                  className="bg-blue-600 text-white px-6 py-3 rounded-xl hover:bg-blue-700 transition-colors inline-flex items-center gap-2"
                >
                  <Plus className="h-5 w-5" />
                  Create Your First Resume
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {resumes.map((resume, index) => {
                  const lastModified = new Date(resume.updatedAt).toLocaleDateString();
                  const isPublic = resume.isPublic;

                  return (
                    <motion.div
                      key={resume.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 + index * 0.1 }}
                      whileHover={{ y: -5, scale: 1.02 }}
                      className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
                    >
                      <div className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="text-lg font-bold text-gray-900 truncate">
                                {resume.title}
                              </h3>
                              {isPublic && (
                                <div className="flex items-center gap-1">
                                  <Eye className="h-4 w-4 text-green-600" />
                                  <span className="text-xs text-green-600">Public</span>
                                </div>
                              )}
                            </div>
                            <p className="text-sm text-gray-500 mb-3">
                              {resume.firstName} {resume.lastName}
                            </p>

                            {/* Template Info */}
                            {resume.template && (
                              <div className="mb-4">
                                <span className="inline-block px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                  {resume.template.name} Template
                                </span>
                              </div>
                            )}

                            {/* Last Modified */}
                            <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                              <div className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                <span>Modified {lastModified}</span>
                              </div>
                            </div>

                            {/* Status Badge */}
                            <span className={`inline-block px-3 py-1 text-xs font-medium rounded-full ${
                              isPublic
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {isPublic ? 'Published' : 'Draft'}
                            </span>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2">
                          <Link
                            href={`/resume-builder/edit/${resume.id}`}
                            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors text-sm font-medium text-center flex items-center justify-center gap-2"
                          >
                            <Edit3 className="h-4 w-4" />
                            Edit
                          </Link>
                          <button
                            onClick={() => handleDownloadResume(resume)}
                            disabled={downloading === resume.id}
                            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-xl hover:bg-gray-200 transition-colors text-sm disabled:opacity-50"
                          >
                            {downloading === resume.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Download className="h-4 w-4" />
                            )}
                          </button>
                          <button
                            onClick={() => handleDeleteResume(resume.id)}
                            disabled={deleting === resume.id}
                            className="bg-red-100 text-red-700 px-4 py-2 rounded-xl hover:bg-red-200 transition-colors text-sm disabled:opacity-50"
                          >
                            {deleting === resume.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
}
