'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Loader2,
  AlertCircle,
  Save,
  Eye,
  Download,
  User,
  Mail,
  Phone,
  MapPin,
  Globe,
  Linkedin,
  FileText,
  Briefcase,
  GraduationCap,
  Award,
  Code,
  Plus,
  Edit3,
  Trash2,
  CheckCircle
} from 'lucide-react';
import Link from 'next/link';
import SkillForm from '@/components/resume/SkillForm';
import ProjectForm from '@/components/resume/ProjectForm';

interface PageProps {
  params: {
    id: string;
  };
}

interface Resume {
  id: string;
  title: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location?: string;
  website?: string;
  linkedin?: string;
  summary?: string;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  template?: {
    id: string;
    name: string;
    category: string;
  };
  experiences?: Experience[];
  educations?: Education[];
  skills?: Skill[];
  projects?: Project[];
}

interface Experience {
  id: string;
  company: string;
  position: string;
  startDate: string;
  endDate?: string;
  isCurrent: boolean;
  description?: string;
  achievements?: string[];
  sortOrder: number;
}

interface Education {
  id: string;
  institution: string;
  degree: string;
  fieldOfStudy?: string;
  startDate: string;
  endDate?: string;
  isCurrent: boolean;
  gpa?: string;
  achievements?: string[];
  sortOrder: number;
}

interface Skill {
  id: string;
  skillName: string;
  category: string;
  proficiency?: number;
  sortOrder: number;
}

interface Project {
  id: string;
  name: string;
  description?: string;
  technologies?: string[];
  projectUrl?: string;
  repositoryUrl?: string;
  startDate?: string;
  endDate?: string;
  isHighlighted: boolean;
  sortOrder: number;
}

export default function EditResumePage({ params }: PageProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [resume, setResume] = useState<Resume | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('personal');
  const [success, setSuccess] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [downloading, setDownloading] = useState(false);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    if (status === 'authenticated') {
      fetchResume();
    }
  }, [status, params.id]);

  const fetchResume = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/resume-builder/${params.id}`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Resume not found');
        }
        throw new Error('Failed to load resume');
      }

      const result = await response.json();

      if (result.success) {
        setResume(result.data);
      } else {
        throw new Error(result.error || 'Failed to load resume');
      }
    } catch (error) {
      console.error('Error fetching resume:', error);
      setError(error instanceof Error ? error.message : 'Failed to load resume');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!resume) return;

    try {
      setSaving(true);
      setError(null);

      const response = await fetch('/api/resume-builder', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: resume.id,
          title: resume.title,
          firstName: resume.firstName,
          lastName: resume.lastName,
          email: resume.email,
          phone: resume.phone,
          location: resume.location,
          website: resume.website,
          linkedin: resume.linkedin,
          summary: resume.summary,
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        throw new Error(result.error || 'Failed to save resume');
      }
    } catch (error) {
      console.error('Error saving resume:', error);
      setError(error instanceof Error ? error.message : 'Failed to save resume');
    } finally {
      setSaving(false);
    }
  };

  const updateResumeField = (field: keyof Resume, value: any) => {
    if (!resume) return;
    setResume({ ...resume, [field]: value });
  };

  const handlePreview = () => {
    setShowPreview(true);
  };

  const handleDownload = async () => {
    if (!resume) return;

    try {
      setDownloading(true);

      // Create a simple text-based resume for download
      const resumeContent = `
RESUME - ${resume.firstName} ${resume.lastName}

Contact Information:
Email: ${resume.email}
Phone: ${resume.phone || 'Not provided'}
Location: ${resume.location || 'Not provided'}
Website: ${resume.website || 'Not provided'}
LinkedIn: ${resume.linkedin || 'Not provided'}

Professional Summary:
${resume.summary || 'No summary provided'}

Work Experience:
${resume.experiences?.map(exp => `
• ${exp.position} at ${exp.company}
  ${new Date(exp.startDate).toLocaleDateString()} - ${exp.isCurrent ? 'Present' : new Date(exp.endDate).toLocaleDateString()}
  ${exp.description || ''}
  ${exp.achievements?.map(achievement => `  - ${achievement}`).join('\n') || ''}
`).join('\n') || 'No experience added'}

Education:
${resume.educations?.map(edu => `
• ${edu.degree} in ${edu.fieldOfStudy} - ${edu.institution}
  ${new Date(edu.startDate).toLocaleDateString()} - ${edu.isCurrent ? 'Present' : new Date(edu.endDate).toLocaleDateString()}
  ${edu.gpa ? `GPA: ${edu.gpa}` : ''}
`).join('\n') || 'No education added'}

Skills:
${resume.skills?.map(skill => `• ${skill.skillName} (${skill.category})`).join('\n') || 'No skills added'}

Projects:
${resume.projects?.map(project => `
• ${project.name}${project.isHighlighted ? ' ⭐' : ''}
  ${project.description || ''}
  Technologies: ${project.technologies?.join(', ') || 'None specified'}
  ${project.projectUrl ? `URL: ${project.projectUrl}` : ''}
  ${project.repositoryUrl ? `Repository: ${project.repositoryUrl}` : ''}
`).join('\n') || 'No projects added'}
      `;

      const blob = new Blob([resumeContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${resume.firstName}_${resume.lastName}_Resume.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

    } catch (err) {
      console.error('Error downloading resume:', err);
      setError('Failed to download resume');
    } finally {
      setDownloading(false);
    }
  };

  const tabs = [
    { id: 'personal', name: 'Personal Info', icon: User },
    { id: 'summary', name: 'Summary', icon: FileText },
    { id: 'experience', name: 'Experience', icon: Briefcase },
    { id: 'education', name: 'Education', icon: GraduationCap },
    { id: 'skills', name: 'Skills', icon: Code },
    { id: 'projects', name: 'Projects', icon: Award },
  ];

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading resume...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white rounded-2xl shadow-xl p-8 text-center max-w-md mx-4"
        >
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Error Loading Resume
          </h3>
          <p className="text-gray-600 text-center mb-4">
            {error}
          </p>
          <div className="flex gap-2 justify-center">
            <Link
              href="/resume-builder"
              className="bg-gray-100 text-gray-700 px-4 py-2 rounded-xl hover:bg-gray-200 transition-colors"
            >
              Back to Resumes
            </Link>
            <button
              onClick={fetchResume}
              className="bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <Link
                  href="/resume-builder"
                  className="p-2 bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
                >
                  <ArrowLeft className="h-5 w-5 text-gray-600" />
                </Link>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Edit Resume
                  </h1>
                  <p className="text-gray-600 mt-1">
                    {resume?.title || 'Loading...'}
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-3">
                {success && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="flex items-center gap-2 bg-green-100 text-green-700 px-3 py-2 rounded-xl"
                  >
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm font-medium">Saved!</span>
                  </motion.div>
                )}

                <motion.button
                  onClick={handlePreview}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="bg-gray-100 text-gray-700 px-4 py-2 rounded-xl hover:bg-gray-200 transition-colors flex items-center gap-2"
                >
                  <Eye className="h-4 w-4" />
                  Preview
                </motion.button>

                <motion.button
                  onClick={handleDownload}
                  disabled={downloading}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="bg-gray-100 text-gray-700 px-4 py-2 rounded-xl hover:bg-gray-200 transition-colors flex items-center gap-2 disabled:opacity-50"
                >
                  {downloading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Downloading...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4" />
                      Download
                    </>
                  )}
                </motion.button>

                <motion.button
                  onClick={handleSave}
                  disabled={saving}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {saving ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </motion.button>
              </div>
            </div>
          </motion.div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar Navigation */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="lg:col-span-1"
            >
              <div className="bg-white rounded-2xl shadow-lg p-6 sticky top-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Resume Sections</h3>
                <nav className="space-y-2">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <motion.button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className={`w-full flex items-center gap-3 px-4 py-3 rounded-xl text-left transition-all ${
                          activeTab === tab.id
                            ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md'
                            : 'text-gray-600 hover:bg-gray-50'
                        }`}
                      >
                        <Icon className="h-5 w-5" />
                        <span className="font-medium">{tab.name}</span>
                      </motion.button>
                    );
                  })}
                </nav>
              </div>
            </motion.div>

            {/* Main Editor */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="lg:col-span-3"
            >
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                {resume && (
                  <div className="p-8">
                    {activeTab === 'personal' && (
                      <PersonalInfoTab resume={resume} updateResumeField={updateResumeField} />
                    )}
                    {activeTab === 'summary' && (
                      <SummaryTab resume={resume} updateResumeField={updateResumeField} />
                    )}
                    {activeTab === 'experience' && (
                      <ExperienceTab resume={resume} />
                    )}
                    {activeTab === 'education' && (
                      <EducationTab resume={resume} />
                    )}
                    {activeTab === 'skills' && (
                      <SkillsTab resume={resume} />
                    )}
                    {activeTab === 'projects' && (
                      <ProjectsTab resume={resume} />
                    )}
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Personal Info Tab Component
function PersonalInfoTab({ resume, updateResumeField }: { resume: Resume; updateResumeField: (field: keyof Resume, value: any) => void }) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
          <User className="h-6 w-6 text-blue-600" />
          Personal Information
        </h2>
      </div>

      {/* Resume Title */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <FileText className="inline h-4 w-4 mr-2" />
          Resume Title *
        </label>
        <input
          type="text"
          value={resume.title}
          onChange={(e) => updateResumeField('title', e.target.value)}
          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
          placeholder="e.g., Senior Software Engineer Resume"
        />
      </div>

      {/* Name Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            First Name *
          </label>
          <input
            type="text"
            value={resume.firstName}
            onChange={(e) => updateResumeField('firstName', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
            placeholder="John"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Last Name *
          </label>
          <input
            type="text"
            value={resume.lastName}
            onChange={(e) => updateResumeField('lastName', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
            placeholder="Doe"
          />
        </div>
      </div>

      {/* Contact Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Contact Information</h3>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Mail className="inline h-4 w-4 mr-2" />
            Email Address *
          </label>
          <input
            type="email"
            value={resume.email}
            onChange={(e) => updateResumeField('email', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
            placeholder="<EMAIL>"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Phone */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Phone className="inline h-4 w-4 mr-2" />
              Phone Number
            </label>
            <input
              type="tel"
              value={resume.phone || ''}
              onChange={(e) => updateResumeField('phone', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
              placeholder="+****************"
            />
          </div>

          {/* Location */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <MapPin className="inline h-4 w-4 mr-2" />
              Location
            </label>
            <input
              type="text"
              value={resume.location || ''}
              onChange={(e) => updateResumeField('location', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
              placeholder="San Francisco, CA"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Website */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Globe className="inline h-4 w-4 mr-2" />
              Website
            </label>
            <input
              type="url"
              value={resume.website || ''}
              onChange={(e) => updateResumeField('website', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
              placeholder="https://johndoe.com"
            />
          </div>

          {/* LinkedIn */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Linkedin className="inline h-4 w-4 mr-2" />
              LinkedIn
            </label>
            <input
              type="url"
              value={resume.linkedin || ''}
              onChange={(e) => updateResumeField('linkedin', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
              placeholder="https://linkedin.com/in/johndoe"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

// Summary Tab Component
function SummaryTab({ resume, updateResumeField }: { resume: Resume; updateResumeField: (field: keyof Resume, value: any) => void }) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
          <FileText className="h-6 w-6 text-blue-600" />
          Professional Summary
        </h2>
        <p className="text-gray-600">
          Write a compelling overview of your professional background, key skills, and career objectives.
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Professional Summary
        </label>
        <textarea
          value={resume.summary || ''}
          onChange={(e) => updateResumeField('summary', e.target.value)}
          rows={8}
          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all resize-none"
          placeholder="Write a brief overview of your professional background, key skills, and career objectives. This should be 2-3 sentences that highlight your value proposition to potential employers."
        />
        <div className="flex justify-between items-center mt-2">
          <span className={`text-sm ${(resume.summary?.length || 0) < 50 ? 'text-red-500' : 'text-green-600'}`}>
            {(resume.summary?.length || 0) < 50 ? 'At least 50 characters recommended' : 'Good length!'}
          </span>
          <span className="text-sm text-gray-500">{resume.summary?.length || 0}/1000 characters</span>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
        <div className="flex items-start gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <FileText className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h4 className="font-medium text-blue-900 mb-1">Writing Tips</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Start with your years of experience and key expertise</li>
              <li>• Highlight your most relevant skills and achievements</li>
              <li>• Mention specific industries or technologies you work with</li>
              <li>• Keep it concise but impactful (2-3 sentences)</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

// Experience Tab Component
function ExperienceTab({ resume }: { resume: Resume }) {
  const [experiences, setExperiences] = useState(resume.experiences || []);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingExperience, setEditingExperience] = useState<Experience | null>(null);

  // Fetch experiences on component mount and when resume changes
  useEffect(() => {
    if (resume.experiences) {
      setExperiences(resume.experiences);
    } else {
      fetchExperiences();
    }
  }, [resume.id]);

  const fetchExperiences = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/resume-builder/${resume.id}/experience`);
      const data = await response.json();

      if (response.ok && data.success) {
        setExperiences(data.data);
      } else {
        setError(data.error || 'Failed to fetch experiences');
      }
    } catch (err) {
      console.error('Error fetching experiences:', err);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddExperience = () => {
    setShowAddForm(true);
  };

  const handleExperienceAdded = (newExperience: any) => {
    setExperiences([...experiences, newExperience]);
    setShowAddForm(false);
  };

  const handleEditExperience = (experience: Experience) => {
    setEditingExperience(experience);
  };

  const handleExperienceUpdated = (updatedExperience: any) => {
    setExperiences(experiences.map(exp =>
      exp.id === updatedExperience.id ? updatedExperience : exp
    ));
    setEditingExperience(null);
  };

  const handleDeleteExperience = async (experienceId: string) => {
    if (!confirm('Are you sure you want to delete this experience?')) {
      return;
    }

    try {
      const response = await fetch(
        `/api/resume-builder/${resume.id}/experience?experienceId=${experienceId}`,
        { method: 'DELETE' }
      );

      const data = await response.json();

      if (response.ok && data.success) {
        setExperiences(experiences.filter(exp => exp.id !== experienceId));
      } else {
        alert(data.error || 'Failed to delete experience');
      }
    } catch (err) {
      console.error('Error deleting experience:', err);
      alert('Network error. Please try again.');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
          <Briefcase className="h-6 w-6 text-blue-600" />
          Work Experience
        </h2>
        <button
          onClick={handleAddExperience}
          className="bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Experience
        </button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading experiences...</span>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4 text-center">
          <AlertCircle className="h-6 w-6 text-red-600 mx-auto mb-2" />
          <p className="text-red-700">{error}</p>
          <button
            onClick={fetchExperiences}
            className="mt-2 bg-red-600 text-white px-4 py-2 rounded-xl hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      ) : experiences.length === 0 ? (
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 text-center">
          <Briefcase className="h-12 w-12 text-blue-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-blue-900 mb-2">No Experience Added Yet</h3>
          <p className="text-blue-700 mb-4">
            Add your work experience to showcase your professional background.
          </p>
          <button
            onClick={handleAddExperience}
            className="bg-blue-600 text-white px-6 py-2 rounded-xl hover:bg-blue-700 transition-colors flex items-center gap-2 mx-auto"
          >
            <Plus className="h-4 w-4" />
            Add Your First Experience
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {experiences.map((experience) => (
            <div key={experience.id} className="bg-white border border-gray-200 rounded-xl p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">{experience.position}</h3>
                  <p className="text-blue-600 font-medium">{experience.company}</p>
                  <p className="text-sm text-gray-500">
                    {new Date(experience.startDate).toLocaleDateString()} - {
                      experience.isCurrent
                        ? 'Present'
                        : new Date(experience.endDate).toLocaleDateString()
                    }
                  </p>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleEditExperience(experience)}
                    className="text-gray-400 hover:text-blue-600 transition-colors"
                  >
                    <Edit3 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteExperience(experience.id)}
                    className="text-gray-400 hover:text-red-600 transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {experience.description && (
                <p className="text-gray-700 mb-3">{experience.description}</p>
              )}

              {experience.achievements && experience.achievements.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Key Achievements:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    {experience.achievements.map((achievement, index) => (
                      <li key={index} className="text-gray-700 text-sm">{achievement}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {showAddForm && (
        <ExperienceForm
          resumeId={resume.id}
          onSuccess={handleExperienceAdded}
          onCancel={() => setShowAddForm(false)}
        />
      )}

      {editingExperience && (
        <ExperienceForm
          resumeId={resume.id}
          experience={editingExperience}
          onSuccess={handleExperienceUpdated}
          onCancel={() => setEditingExperience(null)}
        />
      )}
    </div>
  );
}

// Education Tab Component
function EducationTab({ resume }: { resume: Resume }) {
  const [educations, setEducations] = useState(resume.educations || []);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingEducation, setEditingEducation] = useState<Education | null>(null);

  // Fetch educations on component mount and when resume changes
  useEffect(() => {
    if (resume.educations) {
      setEducations(resume.educations);
    } else {
      fetchEducations();
    }
  }, [resume.id]);

  const fetchEducations = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/resume-builder/${resume.id}/education`);
      const data = await response.json();

      if (response.ok && data.success) {
        setEducations(data.data);
      } else {
        setError(data.error || 'Failed to fetch education entries');
      }
    } catch (err) {
      console.error('Error fetching education:', err);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddEducation = () => {
    setShowAddForm(true);
  };

  const handleEducationAdded = (newEducation: any) => {
    setEducations([...educations, newEducation]);
    setShowAddForm(false);
  };

  const handleEditEducation = (education: Education) => {
    setEditingEducation(education);
  };

  const handleEducationUpdated = (updatedEducation: any) => {
    setEducations(educations.map(edu =>
      edu.id === updatedEducation.id ? updatedEducation : edu
    ));
    setEditingEducation(null);
  };

  const handleDeleteEducation = async (educationId: string) => {
    if (!confirm('Are you sure you want to delete this education entry?')) {
      return;
    }

    try {
      const response = await fetch(
        `/api/resume-builder/${resume.id}/education?educationId=${educationId}`,
        { method: 'DELETE' }
      );

      const data = await response.json();

      if (response.ok && data.success) {
        setEducations(educations.filter(edu => edu.id !== educationId));
      } else {
        alert(data.error || 'Failed to delete education entry');
      }
    } catch (err) {
      console.error('Error deleting education:', err);
      alert('Network error. Please try again.');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
          <GraduationCap className="h-6 w-6 text-blue-600" />
          Education
        </h2>
        <button
          onClick={handleAddEducation}
          className="bg-green-600 text-white px-4 py-2 rounded-xl hover:bg-green-700 transition-colors flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Education
        </button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-green-600" />
          <span className="ml-2 text-gray-600">Loading education...</span>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4 text-center">
          <AlertCircle className="h-6 w-6 text-red-600 mx-auto mb-2" />
          <p className="text-red-700">{error}</p>
          <button
            onClick={fetchEducations}
            className="mt-2 bg-red-600 text-white px-4 py-2 rounded-xl hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      ) : educations.length === 0 ? (
        <div className="bg-green-50 border border-green-200 rounded-xl p-6 text-center">
          <GraduationCap className="h-12 w-12 text-green-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-green-900 mb-2">No Education Added Yet</h3>
          <p className="text-green-700 mb-4">
            Add your educational background, degrees, and certifications.
          </p>
          <button
            onClick={handleAddEducation}
            className="bg-green-600 text-white px-6 py-2 rounded-xl hover:bg-green-700 transition-colors flex items-center gap-2 mx-auto"
          >
            <Plus className="h-4 w-4" />
            Add Your First Education
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {educations.map((education) => (
            <div key={education.id} className="bg-white border border-gray-200 rounded-xl p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">{education.degree}</h3>
                  <p className="text-green-600 font-medium">{education.institution}</p>
                  {education.fieldOfStudy && (
                    <p className="text-gray-600">{education.fieldOfStudy}</p>
                  )}
                  <p className="text-sm text-gray-500">
                    {new Date(education.startDate).toLocaleDateString()} - {
                      education.isCurrent
                        ? 'Present'
                        : education.endDate ? new Date(education.endDate).toLocaleDateString() : 'N/A'
                    }
                  </p>
                  {education.gpa && (
                    <p className="text-sm text-gray-600 mt-1">GPA: {education.gpa}</p>
                  )}
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleEditEducation(education)}
                    className="text-gray-400 hover:text-green-600 transition-colors"
                  >
                    <Edit3 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteEducation(education.id)}
                    className="text-gray-400 hover:text-red-600 transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {education.achievements && education.achievements.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Achievements:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    {education.achievements.map((achievement, index) => (
                      <li key={index} className="text-gray-700 text-sm">{achievement}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {showAddForm && (
        <EducationForm
          resumeId={resume.id}
          onSuccess={handleEducationAdded}
          onCancel={() => setShowAddForm(false)}
        />
      )}

      {editingEducation && (
        <EducationForm
          resumeId={resume.id}
          education={editingEducation}
          onSuccess={handleEducationUpdated}
          onCancel={() => setEditingEducation(null)}
        />
      )}
    </div>
  );
}

// Skills Tab Component
function SkillsTab({ resume }: { resume: Resume }) {
  const [skillsData, setSkillsData] = useState<{skills: Skill[], skillsByCategory: Record<string, Skill[]>}>({
    skills: resume.skills || [],
    skillsByCategory: {}
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingSkill, setEditingSkill] = useState<Skill | null>(null);

  // Fetch skills on component mount and when resume changes
  useEffect(() => {
    if (resume.skills && resume.skills.length > 0) {
      const skillsByCategory = resume.skills.reduce((acc, skill) => {
        if (!acc[skill.category]) acc[skill.category] = [];
        acc[skill.category].push(skill);
        return acc;
      }, {} as Record<string, Skill[]>);

      setSkillsData({
        skills: resume.skills,
        skillsByCategory
      });
    } else {
      fetchSkills();
    }
  }, [resume.id]);

  const fetchSkills = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/resume-builder/${resume.id}/skills`);
      const data = await response.json();

      if (response.ok && data.success) {
        setSkillsData({
          skills: data.data.skills,
          skillsByCategory: data.data.skillsByCategory
        });
      } else {
        setError(data.error || 'Failed to fetch skills');
      }
    } catch (err) {
      console.error('Error fetching skills:', err);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddSkill = () => {
    setShowAddForm(true);
  };

  const handleSkillAdded = (newSkill: any) => {
    const updatedSkills = [...skillsData.skills, newSkill];
    const updatedByCategory = updatedSkills.reduce((acc, skill) => {
      if (!acc[skill.category]) acc[skill.category] = [];
      acc[skill.category].push(skill);
      return acc;
    }, {} as Record<string, Skill[]>);

    setSkillsData({
      skills: updatedSkills,
      skillsByCategory: updatedByCategory
    });
    setShowAddForm(false);
  };

  const handleEditSkill = (skill: Skill) => {
    setEditingSkill(skill);
  };

  const handleSkillUpdated = (updatedSkill: any) => {
    const updatedSkills = skillsData.skills.map(skill =>
      skill.id === updatedSkill.id ? updatedSkill : skill
    );
    const updatedByCategory = updatedSkills.reduce((acc, skill) => {
      if (!acc[skill.category]) acc[skill.category] = [];
      acc[skill.category].push(skill);
      return acc;
    }, {} as Record<string, Skill[]>);

    setSkillsData({
      skills: updatedSkills,
      skillsByCategory: updatedByCategory
    });
    setEditingSkill(null);
  };

  const handleDeleteSkill = async (skillId: string) => {
    if (!confirm('Are you sure you want to delete this skill?')) {
      return;
    }

    try {
      const response = await fetch(
        `/api/resume-builder/${resume.id}/skills?skillId=${skillId}`,
        { method: 'DELETE' }
      );

      const data = await response.json();

      if (response.ok && data.success) {
        const updatedSkills = skillsData.skills.filter(skill => skill.id !== skillId);
        const updatedByCategory = updatedSkills.reduce((acc, skill) => {
          if (!acc[skill.category]) acc[skill.category] = [];
          acc[skill.category].push(skill);
          return acc;
        }, {} as Record<string, Skill[]>);

        setSkillsData({
          skills: updatedSkills,
          skillsByCategory: updatedByCategory
        });
      } else {
        alert(data.error || 'Failed to delete skill');
      }
    } catch (err) {
      console.error('Error deleting skill:', err);
      alert('Network error. Please try again.');
    }
  };

  const getProficiencyLabel = (level: number) => {
    const labels = ['', 'Beginner', 'Intermediate', 'Advanced', 'Expert', 'Master'];
    return labels[level] || 'Unknown';
  };

  const getProficiencyColor = (level: number) => {
    const colors = ['', 'bg-red-500', 'bg-yellow-500', 'bg-blue-500', 'bg-green-500', 'bg-purple-500'];
    return colors[level] || 'bg-gray-500';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
          <Code className="h-6 w-6 text-blue-600" />
          Skills & Technologies
        </h2>
        <button
          onClick={handleAddSkill}
          className="bg-purple-600 text-white px-4 py-2 rounded-xl hover:bg-purple-700 transition-colors flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Skill
        </button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-purple-600" />
          <span className="ml-2 text-gray-600">Loading skills...</span>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4 text-center">
          <AlertCircle className="h-6 w-6 text-red-600 mx-auto mb-2" />
          <p className="text-red-700">{error}</p>
          <button
            onClick={fetchSkills}
            className="mt-2 bg-red-600 text-white px-4 py-2 rounded-xl hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      ) : skillsData.skills.length === 0 ? (
        <div className="bg-purple-50 border border-purple-200 rounded-xl p-6 text-center">
          <Code className="h-12 w-12 text-purple-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-purple-900 mb-2">No Skills Added Yet</h3>
          <p className="text-purple-700 mb-4">
            Showcase your technical skills, programming languages, and tools.
          </p>
          <button
            onClick={handleAddSkill}
            className="bg-purple-600 text-white px-6 py-2 rounded-xl hover:bg-purple-700 transition-colors flex items-center gap-2 mx-auto"
          >
            <Plus className="h-4 w-4" />
            Add Your First Skill
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          {Object.entries(skillsData.skillsByCategory).map(([category, skills]) => (
            <div key={category} className="bg-white border border-gray-200 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 capitalize">{category}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {skills.map((skill) => (
                  <div key={skill.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900">{skill.skillName}</span>
                        {skill.proficiency && (
                          <span className={`px-2 py-1 text-xs font-medium text-white rounded-full ${getProficiencyColor(skill.proficiency)}`}>
                            {getProficiencyLabel(skill.proficiency)}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <button
                        onClick={() => handleEditSkill(skill)}
                        className="text-gray-400 hover:text-purple-600 transition-colors"
                      >
                        <Edit3 className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => handleDeleteSkill(skill.id)}
                        className="text-gray-400 hover:text-red-600 transition-colors"
                      >
                        <Trash2 className="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {showAddForm && (
        <SkillForm
          resumeId={resume.id}
          onSuccess={handleSkillAdded}
          onCancel={() => setShowAddForm(false)}
        />
      )}

      {editingSkill && (
        <SkillForm
          resumeId={resume.id}
          skill={editingSkill}
          onSuccess={handleSkillUpdated}
          onCancel={() => setEditingSkill(null)}
        />
      )}
    </div>
  );
}

// Projects Tab Component
function ProjectsTab({ resume }: { resume: Resume }) {
  const [projectsData, setProjectsData] = useState<{projects: Project[], total: number, highlighted: number}>({
    projects: resume.projects || [],
    total: 0,
    highlighted: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);

  // Fetch projects on component mount and when resume changes
  useEffect(() => {
    if (resume.projects && resume.projects.length > 0) {
      setProjectsData({
        projects: resume.projects,
        total: resume.projects.length,
        highlighted: resume.projects.filter(p => p.isHighlighted).length
      });
    } else {
      fetchProjects();
    }
  }, [resume.id]);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/resume-builder/${resume.id}/projects`);
      const data = await response.json();

      if (response.ok && data.success) {
        setProjectsData({
          projects: data.data.projects,
          total: data.data.total,
          highlighted: data.data.highlighted
        });
      } else {
        setError(data.error || 'Failed to fetch projects');
      }
    } catch (err) {
      console.error('Error fetching projects:', err);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddProject = () => {
    setShowAddForm(true);
  };

  const handleProjectAdded = (newProject: any) => {
    const updatedProjects = [...projectsData.projects, newProject];
    setProjectsData({
      projects: updatedProjects,
      total: updatedProjects.length,
      highlighted: updatedProjects.filter(p => p.isHighlighted).length
    });
    setShowAddForm(false);
  };

  const handleEditProject = (project: Project) => {
    setEditingProject(project);
  };

  const handleProjectUpdated = (updatedProject: any) => {
    const updatedProjects = projectsData.projects.map(proj =>
      proj.id === updatedProject.id ? updatedProject : proj
    );
    setProjectsData({
      projects: updatedProjects,
      total: updatedProjects.length,
      highlighted: updatedProjects.filter(p => p.isHighlighted).length
    });
    setEditingProject(null);
  };

  const handleDeleteProject = async (projectId: string) => {
    if (!confirm('Are you sure you want to delete this project?')) {
      return;
    }

    try {
      const response = await fetch(
        `/api/resume-builder/${resume.id}/projects?projectId=${projectId}`,
        { method: 'DELETE' }
      );

      const data = await response.json();

      if (response.ok && data.success) {
        const updatedProjects = projectsData.projects.filter(project => project.id !== projectId);
        setProjectsData({
          projects: updatedProjects,
          total: updatedProjects.length,
          highlighted: updatedProjects.filter(p => p.isHighlighted).length
        });
      } else {
        alert(data.error || 'Failed to delete project');
      }
    } catch (err) {
      console.error('Error deleting project:', err);
      alert('Network error. Please try again.');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
          <Award className="h-6 w-6 text-blue-600" />
          Projects & Portfolio
        </h2>
        <button
          onClick={handleAddProject}
          className="bg-orange-600 text-white px-4 py-2 rounded-xl hover:bg-orange-700 transition-colors flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Project
        </button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-orange-600" />
          <span className="ml-2 text-gray-600">Loading projects...</span>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4 text-center">
          <AlertCircle className="h-6 w-6 text-red-600 mx-auto mb-2" />
          <p className="text-red-700">{error}</p>
          <button
            onClick={fetchProjects}
            className="mt-2 bg-red-600 text-white px-4 py-2 rounded-xl hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      ) : projectsData.projects.length === 0 ? (
        <div className="bg-orange-50 border border-orange-200 rounded-xl p-6 text-center">
          <Award className="h-12 w-12 text-orange-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-orange-900 mb-2">No Projects Added Yet</h3>
          <p className="text-orange-700 mb-4">
            Showcase your key projects, portfolio pieces, and accomplishments.
          </p>
          <button
            onClick={handleAddProject}
            className="bg-orange-600 text-white px-6 py-2 rounded-xl hover:bg-orange-700 transition-colors flex items-center gap-2 mx-auto"
          >
            <Plus className="h-4 w-4" />
            Add Your First Project
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {projectsData.projects.map((project) => (
            <div key={project.id} className={`bg-white border rounded-xl p-6 ${project.isHighlighted ? 'border-orange-300 bg-orange-50' : 'border-gray-200'}`}>
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{project.name}</h3>
                    {project.isHighlighted && (
                      <span className="px-2 py-1 text-xs font-medium bg-orange-200 text-orange-800 rounded-full">
                        Featured
                      </span>
                    )}
                  </div>
                  {project.description && (
                    <p className="text-gray-700 mb-3">{project.description}</p>
                  )}

                  {project.technologies && project.technologies.length > 0 && (
                    <div className="mb-3">
                      <div className="flex flex-wrap gap-2">
                        {project.technologies.map((tech, index) => (
                          <span key={index} className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    {project.startDate && (
                      <span>
                        {new Date(project.startDate).toLocaleDateString()} - {
                          project.endDate ? new Date(project.endDate).toLocaleDateString() : 'Ongoing'
                        }
                      </span>
                    )}
                    {project.projectUrl && (
                      <a
                        href={project.projectUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-700 flex items-center gap-1"
                      >
                        <Globe className="h-3 w-3" />
                        Live Demo
                      </a>
                    )}
                    {project.repositoryUrl && (
                      <a
                        href={project.repositoryUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-600 hover:text-gray-700 flex items-center gap-1"
                      >
                        <Code className="h-3 w-3" />
                        Repository
                      </a>
                    )}
                  </div>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleEditProject(project)}
                    className="text-gray-400 hover:text-orange-600 transition-colors"
                  >
                    <Edit3 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteProject(project.id)}
                    className="text-gray-400 hover:text-red-600 transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {showAddForm && (
        <ProjectForm
          resumeId={resume.id}
          onSuccess={handleProjectAdded}
          onCancel={() => setShowAddForm(false)}
        />
      )}

      {editingProject && (
        <ProjectForm
          resumeId={resume.id}
          project={editingProject}
          onSuccess={handleProjectUpdated}
          onCancel={() => setEditingProject(null)}
        />
      )}
    </div>
  );
}

// Experience Form Component
function ExperienceForm({
  resumeId,
  experience,
  onSuccess,
  onCancel
}: {
  resumeId: string;
  experience?: Experience;
  onSuccess: (experience: any) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState({
    company: experience?.company || '',
    position: experience?.position || '',
    startDate: experience?.startDate ? new Date(experience.startDate).toISOString().split('T')[0] : '',
    endDate: experience?.endDate ? new Date(experience.endDate).toISOString().split('T')[0] : '',
    isCurrent: experience?.isCurrent || false,
    description: experience?.description || '',
    achievements: experience?.achievements && experience.achievements.length > 0 ? experience.achievements : ['']
  });
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleAchievementChange = (index: number, value: string) => {
    const newAchievements = [...formData.achievements];
    newAchievements[index] = value;
    setFormData(prev => ({ ...prev, achievements: newAchievements }));
  };

  const addAchievement = () => {
    setFormData(prev => ({
      ...prev,
      achievements: [...prev.achievements, '']
    }));
  };

  const removeAchievement = (index: number) => {
    setFormData(prev => ({
      ...prev,
      achievements: prev.achievements.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);
      setErrors({});

      const submitData = {
        ...formData,
        achievements: formData.achievements.filter(a => a.trim() !== ''),
        ...(experience && { id: experience.id })
      };

      const response = await fetch(`/api/resume-builder/${resumeId}/experience`, {
        method: experience ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        onSuccess(data.data);
      } else {
        if (data.details && Array.isArray(data.details)) {
          const serverErrors: Record<string, string> = {};
          data.details.forEach((error: any) => {
            if (error.path && error.path.length > 0) {
              serverErrors[error.path[0]] = error.message;
            }
          });
          setErrors(serverErrors);
        } else {
          setErrors({ general: data.error || 'Failed to create experience' });
        }
      }
    } catch (error) {
      console.error('Error creating experience:', error);
      setErrors({ general: 'Network error. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        {experience ? 'Edit Work Experience' : 'Add Work Experience'}
      </h3>

      <form onSubmit={handleSubmit} className="space-y-4">
        {errors.general && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-red-700 text-sm">{errors.general}</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Company *
            </label>
            <input
              type="text"
              value={formData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Company name"
            />
            {errors.company && (
              <p className="mt-1 text-sm text-red-600">{errors.company}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Position *
            </label>
            <input
              type="text"
              value={formData.position}
              onChange={(e) => handleInputChange('position', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Job title"
            />
            {errors.position && (
              <p className="mt-1 text-sm text-red-600">{errors.position}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date *
            </label>
            <input
              type="date"
              value={formData.startDate}
              onChange={(e) => handleInputChange('startDate', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {errors.startDate && (
              <p className="mt-1 text-sm text-red-600">{errors.startDate}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="date"
              value={formData.endDate}
              onChange={(e) => handleInputChange('endDate', e.target.value)}
              disabled={formData.isCurrent}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
            />
            <div className="mt-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.isCurrent}
                  onChange={(e) => handleInputChange('isCurrent', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">I currently work here</span>
              </label>
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Brief description of your role and responsibilities"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Key Achievements
          </label>
          {formData.achievements.map((achievement, index) => (
            <div key={index} className="flex gap-2 mb-2">
              <input
                type="text"
                value={achievement}
                onChange={(e) => handleAchievementChange(index, e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Describe a key achievement or accomplishment"
              />
              {formData.achievements.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeAchievement(index)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              )}
            </div>
          ))}
          <button
            type="button"
            onClick={addAchievement}
            className="text-blue-600 hover:text-blue-700 text-sm flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            Add Achievement
          </button>
        </div>

        <div className="flex gap-3 pt-4">
          <button
            type="submit"
            disabled={saving}
            className="bg-blue-600 text-white px-6 py-2 rounded-xl hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center gap-2"
          >
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                {experience ? 'Update Experience' : 'Save Experience'}
              </>
            )}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="bg-gray-100 text-gray-700 px-6 py-2 rounded-xl hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}

// Education Form Component
function EducationForm({
  resumeId,
  education,
  onSuccess,
  onCancel
}: {
  resumeId: string;
  education?: Education;
  onSuccess: (education: any) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState({
    institution: education?.institution || '',
    degree: education?.degree || '',
    fieldOfStudy: education?.fieldOfStudy || '',
    startDate: education?.startDate ? new Date(education.startDate).toISOString().split('T')[0] : '',
    endDate: education?.endDate ? new Date(education.endDate).toISOString().split('T')[0] : '',
    isCurrent: education?.isCurrent || false,
    gpa: education?.gpa || '',
    achievements: education?.achievements && education.achievements.length > 0 ? education.achievements : ['']
  });
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleAchievementChange = (index: number, value: string) => {
    const newAchievements = [...formData.achievements];
    newAchievements[index] = value;
    setFormData(prev => ({ ...prev, achievements: newAchievements }));
  };

  const addAchievement = () => {
    setFormData(prev => ({
      ...prev,
      achievements: [...prev.achievements, '']
    }));
  };

  const removeAchievement = (index: number) => {
    setFormData(prev => ({
      ...prev,
      achievements: prev.achievements.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);
      setErrors({});

      const submitData = {
        ...formData,
        achievements: formData.achievements.filter(a => a.trim() !== ''),
        ...(education && { id: education.id })
      };

      const response = await fetch(`/api/resume-builder/${resumeId}/education`, {
        method: education ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        onSuccess(data.data);
      } else {
        if (data.details && Array.isArray(data.details)) {
          const serverErrors: Record<string, string> = {};
          data.details.forEach((error: any) => {
            if (error.path && error.path.length > 0) {
              serverErrors[error.path[0]] = error.message;
            }
          });
          setErrors(serverErrors);
        } else {
          setErrors({ general: data.error || 'Failed to create education entry' });
        }
      }
    } catch (error) {
      console.error('Error creating education:', error);
      setErrors({ general: 'Network error. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        {education ? 'Edit Education' : 'Add Education'}
      </h3>

      <form onSubmit={handleSubmit} className="space-y-4">
        {errors.general && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-red-700 text-sm">{errors.general}</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Institution *
            </label>
            <input
              type="text"
              value={formData.institution}
              onChange={(e) => handleInputChange('institution', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="University name"
            />
            {errors.institution && (
              <p className="mt-1 text-sm text-red-600">{errors.institution}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Degree *
            </label>
            <input
              type="text"
              value={formData.degree}
              onChange={(e) => handleInputChange('degree', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="Bachelor of Science"
            />
            {errors.degree && (
              <p className="mt-1 text-sm text-red-600">{errors.degree}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Field of Study
          </label>
          <input
            type="text"
            value={formData.fieldOfStudy}
            onChange={(e) => handleInputChange('fieldOfStudy', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Computer Science"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date *
            </label>
            <input
              type="date"
              value={formData.startDate}
              onChange={(e) => handleInputChange('startDate', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            />
            {errors.startDate && (
              <p className="mt-1 text-sm text-red-600">{errors.startDate}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="date"
              value={formData.endDate}
              onChange={(e) => handleInputChange('endDate', e.target.value)}
              disabled={formData.isCurrent}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:bg-gray-100"
            />
            <div className="mt-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.isCurrent}
                  onChange={(e) => handleInputChange('isCurrent', e.target.checked)}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="ml-2 text-sm text-gray-700">Currently studying</span>
              </label>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              GPA
            </label>
            <input
              type="text"
              value={formData.gpa}
              onChange={(e) => handleInputChange('gpa', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="3.8"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Achievements & Honors
          </label>
          {formData.achievements.map((achievement, index) => (
            <div key={index} className="flex gap-2 mb-2">
              <input
                type="text"
                value={achievement}
                onChange={(e) => handleAchievementChange(index, e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Dean's List, Magna Cum Laude, etc."
              />
              {formData.achievements.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeAchievement(index)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              )}
            </div>
          ))}
          <button
            type="button"
            onClick={addAchievement}
            className="text-green-600 hover:text-green-700 text-sm flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            Add Achievement
          </button>
        </div>

        <div className="flex gap-3 pt-4">
          <button
            type="submit"
            disabled={saving}
            className="bg-green-600 text-white px-6 py-2 rounded-xl hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center gap-2"
          >
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                {education ? 'Update Education' : 'Save Education'}
              </>
            )}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="bg-gray-100 text-gray-700 px-6 py-2 rounded-xl hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}
