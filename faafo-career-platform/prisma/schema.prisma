generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                   String                     @id @default(cuid())
  name                 String?
  email                String                     @unique
  emailVerified        DateTime?
  image                String?
  password             String
  passwordResetToken   String?                    @unique
  passwordResetExpires DateTime?
  failedLoginAttempts  Int                        @default(0)
  lockedUntil          DateTime?
  createdAt            DateTime                   @default(now())
  updatedAt            DateTime                   @updatedAt
  accounts             Account[]
  assessments          Assessment[]
  careerPathBookmarks  CareerPathBookmark[]
  forumBookmarks       ForumBookmark[]
  moderatorRoles       ForumModerator[]
  forumPosts           ForumPost[]
  forumPostReactions   ForumPostReaction[]
  forumReplies         ForumReply[]
  forumReplyReactions  ForumReplyReaction[]
  forumReports         ForumReport[]
  freedomFund          FreedomFund?
  learningAnalytics    LearningAnalytics[]
  profile              Profile?
  resourceRatings      ResourceRating[]
  sessions             Session[]
  achievements         UserAchievement[]
  goals                UserGoal[]
  learningPaths        UserLearningPath[]
  learningPathProgress UserLearningPathProgress[]
  learningProgress     UserLearningProgress[]
  skillProgress        UserSkillProgress[]
  resumes              Resume[]
}

model Profile {
  id                     String            @id @default(uuid())
  userId                 String            @unique
  bio                    String?
  profilePictureUrl      String?
  socialMediaLinks       Json?
  firstName              String?
  lastName               String?
  phoneNumber            String?
  location               String?
  website                String?
  jobTitle               String?
  company                String?
  currentIndustry        String?
  targetIndustry         String?
  experienceLevel        ExperienceLevel?
  careerInterests        Json?
  skillsToLearn          Json?
  weeklyLearningGoal     Int?
  profileVisibility      ProfileVisibility @default(COMMUNITY_ONLY)
  emailNotifications     Boolean           @default(true)
  profilePublic          Boolean           @default(false)
  showEmail              Boolean           @default(false)
  showPhone              Boolean           @default(false)
  profileCompletionScore Int               @default(0)
  lastProfileUpdate      DateTime?
  forumSignature         String?
  forumBio               String?
  forumReputation        Int               @default(0)
  forumPostCount         Int               @default(0)
  forumReplyCount        Int               @default(0)
  joinedAt               DateTime          @default(now())
  lastActiveAt           DateTime          @default(now())
  currentCareerPath      String?
  progressLevel          String?
  achievements           Json?
  createdAt              DateTime          @default(now())
  updatedAt              DateTime          @updatedAt
  user                   User              @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Assessment {
  id          String               @id @default(uuid())
  userId      String
  status      AssessmentStatus     @default(IN_PROGRESS)
  currentStep Int                  @default(0)
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt
  completedAt DateTime?
  user        User                 @relation(fields: [userId], references: [id])
  responses   AssessmentResponse[]
}

model AssessmentResponse {
  id           String     @id @default(uuid())
  assessmentId String
  questionKey  String
  answerValue  Json
  createdAt    DateTime   @default(now())
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  @@index([assessmentId])
}

model CareerPath {
  id                String               @id @default(uuid())
  name              String               @unique
  slug              String               @unique
  overview          String
  pros              String
  cons              String
  actionableSteps   Json
  isActive          Boolean              @default(true)
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  bookmarks         CareerPathBookmark[]
  suggestionRules   SuggestionRule[]
  relatedIndustries Industry[]           @relation("CareerPathToIndustry")
  learningPaths     LearningPath[]       @relation("CareerPathToLearningPath")
  learningResources LearningResource[]   @relation("CareerPathToLearningResource")
  relatedSkills     Skill[]              @relation("CareerPathToSkill")
}

model Skill {
  id                String              @id @default(uuid())
  name              String              @unique
  description       String?
  category          String?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  userProgress      UserSkillProgress[]
  careerPaths       CareerPath[]        @relation("CareerPathToSkill")
  learningPaths     LearningPath[]      @relation("LearningPathToSkill")
  learningResources LearningResource[]  @relation("SkillToLearningResource")
}

model Industry {
  id          String       @id @default(uuid())
  name        String       @unique
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  careerPaths CareerPath[] @relation("CareerPathToIndustry")
}

model SuggestionRule {
  id           String     @id @default(uuid())
  careerPathId String
  questionKey  String
  answerValue  Json
  weight       Float      @default(1.0)
  notes        String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  careerPath   CareerPath @relation(fields: [careerPathId], references: [id])

  @@index([careerPathId])
}

model ForumPost {
  id               String              @id @default(uuid())
  title            String
  content          String
  authorId         String
  categoryId       String?
  tags             Json?
  viewCount        Int                 @default(0)
  likeCount        Int                 @default(0)
  replyCount       Int                 @default(0)
  isLocked         Boolean             @default(false)
  isHidden         Boolean             @default(false)
  isPinned         Boolean             @default(false)
  moderatedBy      String?
  moderatedAt      DateTime?
  moderationReason String?
  createdAt        DateTime            @default(now())
  updatedAt        DateTime            @updatedAt
  bookmarks        ForumBookmark[]
  author           User                @relation(fields: [authorId], references: [id])
  category         ForumCategory?      @relation(fields: [categoryId], references: [id])
  reactions        ForumPostReaction[]
  replies          ForumReply[]
  reports          ForumReport[]
}

model ForumReply {
  id               String               @id @default(uuid())
  content          String
  authorId         String
  postId           String
  likeCount        Int                  @default(0)
  isHidden         Boolean              @default(false)
  moderatedBy      String?
  moderatedAt      DateTime?
  moderationReason String?
  createdAt        DateTime             @default(now())
  updatedAt        DateTime             @updatedAt
  author           User                 @relation(fields: [authorId], references: [id])
  post             ForumPost            @relation(fields: [postId], references: [id])
  reactions        ForumReplyReaction[]
  reports          ForumReport[]
}

model FreedomFund {
  id                   String   @id @default(uuid())
  userId               String   @unique
  monthlyExpenses      Float
  coverageMonths       Int
  targetSavings        Float
  currentSavingsAmount Float?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  user                 User     @relation(fields: [userId], references: [id])
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model LearningResource {
  id                String                   @id @default(uuid())
  title             String
  description       String
  url               String                   @unique
  type              LearningResourceType
  category          LearningResourceCategory
  skillLevel        SkillLevel
  author            String?
  duration          String?
  cost              LearningResourceCost     @default(FREE)
  format            LearningResourceFormat
  isActive          Boolean                  @default(true)
  createdAt         DateTime                 @default(now())
  updatedAt         DateTime                 @updatedAt
  learningPathSteps LearningPathStep[]
  ratings           ResourceRating[]
  userProgress      UserLearningProgress[]
  careerPaths       CareerPath[]             @relation("CareerPathToLearningResource")
  skills            Skill[]                  @relation("SkillToLearningResource")
}

model UserLearningProgress {
  id          String           @id @default(uuid())
  userId      String
  resourceId  String
  status      ProgressStatus   @default(NOT_STARTED)
  completedAt DateTime?
  notes       String?
  rating      Int?
  review      String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  resource    LearningResource @relation(fields: [resourceId], references: [id])
  user        User             @relation(fields: [userId], references: [id])

  @@unique([userId, resourceId])
}

model ResourceRating {
  id         String           @id @default(uuid())
  userId     String
  resourceId String
  rating     Int
  review     String?
  isHelpful  Boolean?
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt
  resource   LearningResource @relation(fields: [resourceId], references: [id])
  user       User             @relation(fields: [userId], references: [id])

  @@unique([userId, resourceId])
}

model ForumCategory {
  id          String           @id @default(uuid())
  name        String           @unique
  slug        String           @unique
  description String?
  guidelines  String?
  parentId    String?
  icon        String?
  color       String?
  sortOrder   Int              @default(0)
  isActive    Boolean          @default(true)
  postCount   Int              @default(0)
  replyCount  Int              @default(0)
  lastPostAt  DateTime?
  lastPostBy  String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  parent      ForumCategory?   @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    ForumCategory[]  @relation("CategoryHierarchy")
  moderators  ForumModerator[]
  posts       ForumPost[]
}

model ForumPostReaction {
  id        String       @id @default(uuid())
  userId    String
  postId    String
  type      ReactionType
  createdAt DateTime     @default(now())
  post      ForumPost    @relation(fields: [postId], references: [id], onDelete: Cascade)
  user      User         @relation(fields: [userId], references: [id])

  @@unique([userId, postId])
}

model ForumReplyReaction {
  id        String       @id @default(uuid())
  userId    String
  replyId   String
  type      ReactionType
  createdAt DateTime     @default(now())
  reply     ForumReply   @relation(fields: [replyId], references: [id], onDelete: Cascade)
  user      User         @relation(fields: [userId], references: [id])

  @@unique([userId, replyId])
}

model ForumBookmark {
  id        String    @id @default(uuid())
  userId    String
  postId    String
  createdAt DateTime  @default(now())
  post      ForumPost @relation(fields: [postId], references: [id], onDelete: Cascade)
  user      User      @relation(fields: [userId], references: [id])

  @@unique([userId, postId])
}

model CareerPathBookmark {
  id           String     @id @default(uuid())
  userId       String
  careerPathId String
  createdAt    DateTime   @default(now())
  careerPath   CareerPath @relation(fields: [careerPathId], references: [id], onDelete: Cascade)
  user         User       @relation(fields: [userId], references: [id])

  @@unique([userId, careerPathId])
  @@index([userId])
  @@index([careerPathId])
}

model ForumModerator {
  id          String         @id @default(uuid())
  userId      String
  categoryId  String?
  role        ModeratorRole
  permissions Json
  assignedBy  String
  assignedAt  DateTime       @default(now())
  isActive    Boolean        @default(true)
  category    ForumCategory? @relation(fields: [categoryId], references: [id])
  user        User           @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([categoryId])
}

model ForumReport {
  id          String       @id @default(uuid())
  reporterId  String
  postId      String?
  replyId     String?
  reason      ReportReason
  description String?
  status      ReportStatus @default(PENDING)
  reviewedBy  String?
  reviewedAt  DateTime?
  resolution  String?
  createdAt   DateTime     @default(now())
  post        ForumPost?   @relation(fields: [postId], references: [id])
  reply       ForumReply?  @relation(fields: [replyId], references: [id])
  reporter    User         @relation(fields: [reporterId], references: [id])

  @@index([status])
  @@index([createdAt])
}

model UserGoal {
  id           String       @id @default(uuid())
  userId       String
  title        String
  description  String?
  type         GoalType
  category     GoalCategory
  status       GoalStatus   @default(ACTIVE)
  targetValue  Int
  currentValue Int          @default(0)
  targetDate   DateTime?
  isPublic     Boolean      @default(false)
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
}

model Achievement {
  id               String            @id @default(uuid())
  title            String            @unique
  description      String
  icon             String
  type             AchievementType
  criteria         Json
  points           Int               @default(0)
  isActive         Boolean           @default(true)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  userAchievements UserAchievement[]
}

model UserAchievement {
  id            String      @id @default(uuid())
  userId        String
  achievementId String
  unlockedAt    DateTime    @default(now())
  progress      Json?
  achievement   Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade)
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, achievementId])
  @@index([userId])
}

model LearningPath {
  id             String                   @id @default(uuid())
  title          String
  description    String
  slug           String                   @unique
  difficulty     SkillLevel
  estimatedHours Int
  prerequisites  Json?
  isActive       Boolean                  @default(true)
  createdBy      String?
  tags           Json?
  imageUrl       String?
  category       LearningResourceCategory
  createdAt      DateTime                 @default(now())
  updatedAt      DateTime                 @updatedAt
  steps          LearningPathStep[]
  userPaths      UserLearningPath[]
  careerPaths    CareerPath[]             @relation("CareerPathToLearningPath")
  skills         Skill[]                  @relation("LearningPathToSkill")
}

model LearningPathStep {
  id               String                     @id @default(uuid())
  learningPathId   String
  title            String
  description      String
  stepOrder        Int
  stepType         LearningStepType
  estimatedMinutes Int
  resourceId       String?
  externalUrl      String?
  content          Json?
  isRequired       Boolean                    @default(true)
  prerequisites    Json?
  createdAt        DateTime                   @default(now())
  updatedAt        DateTime                   @updatedAt
  learningPath     LearningPath               @relation(fields: [learningPathId], references: [id], onDelete: Cascade)
  resource         LearningResource?          @relation(fields: [resourceId], references: [id])
  userProgress     UserLearningPathProgress[]

  @@index([learningPathId, stepOrder])
}

model UserLearningPath {
  id              String                     @id @default(uuid())
  userId          String
  learningPathId  String
  status          LearningPathStatus         @default(NOT_STARTED)
  startedAt       DateTime?
  completedAt     DateTime?
  lastAccessedAt  DateTime                   @default(now())
  currentStepId   String?
  completedSteps  Int                        @default(0)
  totalSteps      Int                        @default(0)
  progressPercent Int                        @default(0)
  totalTimeSpent  Int                        @default(0)
  notes           String?
  rating          Int?
  review          String?
  createdAt       DateTime                   @default(now())
  updatedAt       DateTime                   @updatedAt
  learningPath    LearningPath               @relation(fields: [learningPathId], references: [id], onDelete: Cascade)
  user            User                       @relation(fields: [userId], references: [id], onDelete: Cascade)
  stepProgress    UserLearningPathProgress[]

  @@unique([userId, learningPathId])
  @@index([userId, status])
}

model UserLearningPathProgress {
  id                 String           @id @default(uuid())
  userId             String
  userLearningPathId String
  stepId             String
  status             ProgressStatus   @default(NOT_STARTED)
  startedAt          DateTime?
  completedAt        DateTime?
  timeSpent          Int              @default(0)
  score              Int?
  attempts           Int              @default(0)
  notes              String?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  step               LearningPathStep @relation(fields: [stepId], references: [id], onDelete: Cascade)
  user               User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  userLearningPath   UserLearningPath @relation(fields: [userLearningPathId], references: [id], onDelete: Cascade)

  @@unique([userId, stepId])
  @@index([userId, status])
}

model UserSkillProgress {
  id                 String     @id @default(uuid())
  userId             String
  skillId            String
  currentLevel       SkillLevel @default(BEGINNER)
  progressPoints     Int        @default(0)
  lastPracticed      DateTime?
  completedResources Int        @default(0)
  completedPaths     Int        @default(0)
  practiceHours      Int        @default(0)
  selfAssessment     Int?
  peerValidations    Int        @default(0)
  certifications     Json?
  createdAt          DateTime   @default(now())
  updatedAt          DateTime   @updatedAt
  skill              Skill      @relation(fields: [skillId], references: [id], onDelete: Cascade)
  user               User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, skillId])
  @@index([userId, currentLevel])
}

// Resume/Portfolio Builder Models - Properly Normalized
model Resume {
  id                String              @id @default(uuid())
  userId            String
  title             String              @db.VarChar(100)
  templateId        String?
  // Personal Information - normalized
  firstName         String              @db.VarChar(50)
  lastName          String              @db.VarChar(50)
  email             String              @db.VarChar(255)
  phone             String?             @db.VarChar(20)
  location          String?             @db.VarChar(100)
  website           String?             @db.VarChar(255)
  linkedin          String?             @db.VarChar(255)
  summary           String?             @db.Text
  isPublic          Boolean             @default(false)
  isActive          Boolean             @default(true)
  lastOptimized     DateTime?
  atsScore          Int?                @db.SmallInt
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relationships
  user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  template          ResumeTemplate?     @relation(fields: [templateId], references: [id])
  experiences       ResumeExperience[]
  educations        ResumeEducation[]
  skills            ResumeSkill[]
  projects          ResumeProject[]
  optimizations     ResumeOptimization[]

  @@index([userId])
  @@index([isActive])
  @@map("resumes")
}

model ResumeExperience {
  id            String    @id @default(uuid())
  resumeId      String
  company       String    @db.VarChar(100)
  position      String    @db.VarChar(100)
  startDate     DateTime
  endDate       DateTime?
  isCurrent     Boolean   @default(false)
  description   String?   @db.Text
  achievements  String[]  // PostgreSQL array
  sortOrder     Int       @default(0)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  resume        Resume    @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@index([resumeId])
  @@map("resume_experiences")
}

model ResumeEducation {
  id            String    @id @default(uuid())
  resumeId      String
  institution   String    @db.VarChar(100)
  degree        String    @db.VarChar(100)
  fieldOfStudy  String?   @db.VarChar(100)
  startDate     DateTime
  endDate       DateTime?
  gpa           String?   @db.VarChar(10)
  honors        String[]  // PostgreSQL array
  sortOrder     Int       @default(0)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  resume        Resume    @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@index([resumeId])
  @@map("resume_educations")
}

model ResumeSkill {
  id            String    @id @default(uuid())
  resumeId      String
  category      String    @db.VarChar(50)
  skillName     String    @db.VarChar(50)
  proficiency   Int?      @db.SmallInt // 1-5 scale
  sortOrder     Int       @default(0)
  createdAt     DateTime  @default(now())

  resume        Resume    @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@unique([resumeId, category, skillName])
  @@index([resumeId])
  @@map("resume_skills")
}

model ResumeProject {
  id            String    @id @default(uuid())
  resumeId      String
  name          String    @db.VarChar(100)
  description   String?   @db.Text
  technologies  String[]  // PostgreSQL array
  projectUrl    String?   @db.VarChar(255)
  repositoryUrl String?   @db.VarChar(255)
  startDate     DateTime?
  endDate       DateTime?
  isHighlighted Boolean   @default(false)
  sortOrder     Int       @default(0)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  resume        Resume    @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@index([resumeId])
  @@map("resume_projects")
}

model ResumeOptimization {
  id              String    @id @default(uuid())
  resumeId        String
  overallScore    Int       @db.SmallInt
  atsScore        Int       @db.SmallInt
  suggestions     Json      // Structured suggestions
  keywordAnalysis Json      // Keyword analysis results
  targetRole      String?   @db.VarChar(100)
  targetIndustry  String?   @db.VarChar(50)
  createdAt       DateTime  @default(now())

  resume          Resume    @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@index([resumeId])
  @@index([createdAt])
  @@map("resume_optimizations")
}



model ResumeTemplate {
  id          String    @id @default(uuid())
  name        String    @db.VarChar(50)
  description String?   @db.Text
  category    String    @db.VarChar(30) // professional, creative, academic
  isActive    Boolean   @default(true)
  isPremium   Boolean   @default(false)
  cssStyles   String?   @db.Text // Simple CSS for styling
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  resumes     Resume[]

  @@index([isActive])
  @@index([category])
  @@map("resume_templates")
}

model LearningAnalytics {
  id                String   @id @default(uuid())
  userId            String
  date              DateTime @default(now())
  timeSpent         Int      @default(0)
  resourcesViewed   Int      @default(0)
  pathsProgressed   Int      @default(0)
  skillsImproved    Int      @default(0)
  loginStreak       Int      @default(0)
  weeklyGoalMet     Boolean  @default(false)
  monthlyGoalMet    Boolean  @default(false)
  avgCompletionTime Float?
  learningVelocity  Float?
  deviceType        String?
  sessionDuration   Int?
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@index([userId, date])
}

enum ExperienceLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum ProfileVisibility {
  PRIVATE
  PUBLIC
  COMMUNITY_ONLY
}

enum AssessmentStatus {
  IN_PROGRESS
  COMPLETED
}

enum LearningResourceType {
  COURSE
  ARTICLE
  VIDEO
  PODCAST
  BOOK
  CERTIFICATION
  TUTORIAL
  WORKSHOP
}

enum LearningResourceCategory {
  CYBERSECURITY
  DATA_SCIENCE
  BLOCKCHAIN
  PROJECT_MANAGEMENT
  DIGITAL_MARKETING
  FINANCIAL_LITERACY
  LANGUAGE_LEARNING
  ARTIFICIAL_INTELLIGENCE
  WEB_DEVELOPMENT
  MOBILE_DEVELOPMENT
  CLOUD_COMPUTING
  ENTREPRENEURSHIP
  UX_UI_DESIGN
  PRODUCT_MANAGEMENT
  DEVOPS
}

enum SkillLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum LearningResourceCost {
  FREE
  FREEMIUM
  PAID
  SUBSCRIPTION
}

enum LearningResourceFormat {
  SELF_PACED
  INSTRUCTOR_LED
  INTERACTIVE
  HANDS_ON
  THEORETICAL
}

enum ProgressStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  BOOKMARKED
}

enum ReactionType {
  LIKE
  HELPFUL
  INSIGHTFUL
  FUNNY
  LOVE
  DISAGREE
}

enum ModeratorRole {
  MODERATOR
  ADMIN
  SUPER_ADMIN
}

enum ReportReason {
  SPAM
  INAPPROPRIATE_CONTENT
  HARASSMENT
  OFF_TOPIC
  MISINFORMATION
  COPYRIGHT_VIOLATION
  OTHER
}

enum ReportStatus {
  PENDING
  UNDER_REVIEW
  RESOLVED
  DISMISSED
}

enum GoalType {
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
  CUSTOM
}

enum GoalCategory {
  LEARNING_RESOURCES
  SKILLS
  CERTIFICATIONS
  PROJECTS
  CAREER_MILESTONES
  NETWORKING
}

enum GoalStatus {
  ACTIVE
  COMPLETED
  PAUSED
  CANCELLED
}

enum AchievementType {
  LEARNING_MILESTONE
  STREAK_ACHIEVEMENT
  COMPLETION_BADGE
  COMMUNITY_CONTRIBUTOR
  SKILL_MASTER
  GOAL_ACHIEVER
}

enum LearningStepType {
  RESOURCE
  QUIZ
  ASSIGNMENT
  PROJECT
  DISCUSSION
  REFLECTION
  EXTERNAL_LINK
  VIDEO
  READING
  PRACTICE
}

enum LearningPathStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  PAUSED
  ARCHIVED
}
